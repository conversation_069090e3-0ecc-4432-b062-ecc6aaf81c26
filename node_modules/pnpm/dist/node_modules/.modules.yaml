hoistPattern:
  - '*'
hoistedLocations:
  '@isaacs/cliui@8.0.2':
    - node_modules/@isaacs/cliui
  '@isaacs/fs-minipass@4.0.1':
    - node_modules/@isaacs/fs-minipass
  '@npmcli/agent@3.0.0':
    - node_modules/@npmcli/agent
  '@npmcli/fs@4.0.0':
    - node_modules/@npmcli/fs
  '@pkgjs/parseargs@0.11.0':
    - node_modules/@pkgjs/parseargs
  abbrev@3.0.1:
    - node_modules/abbrev
  agent-base@7.1.3:
    - node_modules/agent-base
  ansi-regex@5.0.1:
    - node_modules/string-width-cjs/node_modules/ansi-regex
    - node_modules/wrap-ansi-cjs/node_modules/ansi-regex
    - node_modules/strip-ansi-cjs/node_modules/ansi-regex
  ansi-regex@6.1.0:
    - node_modules/ansi-regex
  ansi-styles@4.3.0:
    - node_modules/wrap-ansi-cjs/node_modules/ansi-styles
  ansi-styles@6.2.1:
    - node_modules/ansi-styles
  balanced-match@1.0.2:
    - node_modules/balanced-match
  brace-expansion@2.0.2:
    - node_modules/brace-expansion
  cacache@19.0.1:
    - node_modules/cacache
  chownr@3.0.0:
    - node_modules/chownr
  color-convert@2.0.1:
    - node_modules/color-convert
  color-name@1.1.4:
    - node_modules/color-name
  cross-spawn@7.0.6:
    - node_modules/cross-spawn
  debug@4.4.1:
    - node_modules/debug
  eastasianwidth@0.2.0:
    - node_modules/eastasianwidth
  emoji-regex@8.0.0:
    - node_modules/string-width-cjs/node_modules/emoji-regex
    - node_modules/wrap-ansi-cjs/node_modules/emoji-regex
  emoji-regex@9.2.2:
    - node_modules/emoji-regex
  encoding@0.1.13:
    - node_modules/encoding
  env-paths@2.2.1:
    - node_modules/env-paths
  err-code@2.0.3:
    - node_modules/err-code
  exponential-backoff@3.1.2:
    - node_modules/exponential-backoff
  foreground-child@3.3.1:
    - node_modules/foreground-child
  fs-minipass@3.0.3:
    - node_modules/fs-minipass
  glob@10.4.5:
    - node_modules/glob
  graceful-fs@4.2.11:
    - node_modules/graceful-fs
  http-cache-semantics@4.2.0:
    - node_modules/http-cache-semantics
  http-proxy-agent@7.0.2:
    - node_modules/http-proxy-agent
  https-proxy-agent@7.0.6:
    - node_modules/https-proxy-agent
  iconv-lite@0.6.3:
    - node_modules/iconv-lite
  imurmurhash@0.1.4:
    - node_modules/imurmurhash
  ip-address@9.0.5:
    - node_modules/ip-address
  is-fullwidth-code-point@3.0.0:
    - node_modules/is-fullwidth-code-point
  isexe@2.0.0:
    - node_modules/isexe
  isexe@3.1.1:
    - node_modules/node-gyp/node_modules/isexe
  jackspeak@3.4.3:
    - node_modules/jackspeak
  jsbn@1.1.0:
    - node_modules/jsbn
  lru-cache@10.4.3:
    - node_modules/lru-cache
  make-fetch-happen@14.0.3:
    - node_modules/make-fetch-happen
  minimatch@9.0.5:
    - node_modules/minimatch
  minipass-collect@2.0.1:
    - node_modules/minipass-collect
  minipass-fetch@4.0.1:
    - node_modules/minipass-fetch
  minipass-flush@1.0.5:
    - node_modules/minipass-flush
  minipass-pipeline@1.2.4:
    - node_modules/minipass-pipeline
  minipass-sized@1.0.3:
    - node_modules/minipass-sized
  minipass@3.3.6:
    - node_modules/minipass-flush/node_modules/minipass
    - node_modules/minipass-pipeline/node_modules/minipass
    - node_modules/minipass-sized/node_modules/minipass
  minipass@7.1.2:
    - node_modules/minipass
  minizlib@3.0.2:
    - node_modules/minizlib
  mkdirp@3.0.1:
    - node_modules/mkdirp
  ms@2.1.3:
    - node_modules/ms
  negotiator@1.0.0:
    - node_modules/negotiator
  node-gyp@11.1.0:
    - node_modules/node-gyp
  nopt@8.1.0:
    - node_modules/nopt
  p-map@7.0.3:
    - node_modules/p-map
  package-json-from-dist@1.0.1:
    - node_modules/package-json-from-dist
  path-key@3.1.1:
    - node_modules/path-key
  path-scurry@1.11.1:
    - node_modules/path-scurry
  proc-log@5.0.0:
    - node_modules/proc-log
  promise-retry@2.0.1:
    - node_modules/promise-retry
  retry@0.12.0:
    - node_modules/retry
  safer-buffer@2.1.2:
    - node_modules/safer-buffer
  semver@7.7.2:
    - node_modules/semver
  shebang-command@2.0.0:
    - node_modules/shebang-command
  shebang-regex@3.0.0:
    - node_modules/shebang-regex
  signal-exit@4.1.0:
    - node_modules/signal-exit
  smart-buffer@4.2.0:
    - node_modules/smart-buffer
  socks-proxy-agent@8.0.5:
    - node_modules/socks-proxy-agent
  socks@2.8.5:
    - node_modules/socks
  sprintf-js@1.1.3:
    - node_modules/sprintf-js
  ssri@12.0.0:
    - node_modules/ssri
  string-width@4.2.3:
    - node_modules/wrap-ansi-cjs/node_modules/string-width
    - node_modules/string-width-cjs
  string-width@5.1.2:
    - node_modules/string-width
  strip-ansi@6.0.1:
    - node_modules/string-width-cjs/node_modules/strip-ansi
    - node_modules/wrap-ansi-cjs/node_modules/strip-ansi
    - node_modules/strip-ansi-cjs
  strip-ansi@7.1.0:
    - node_modules/strip-ansi
  tar@7.4.3:
    - node_modules/tar
  unique-filename@4.0.0:
    - node_modules/unique-filename
  unique-slug@5.0.0:
    - node_modules/unique-slug
  v8-compile-cache@2.4.0:
    - node_modules/v8-compile-cache
  which@2.0.2:
    - node_modules/which
  which@5.0.0:
    - node_modules/node-gyp/node_modules/which
  wrap-ansi@7.0.0:
    - node_modules/wrap-ansi-cjs
  wrap-ansi@8.1.0:
    - node_modules/wrap-ansi
  yallist@4.0.0:
    - node_modules/minipass-flush/node_modules/yallist
    - node_modules/minipass-pipeline/node_modules/yallist
    - node_modules/minipass-sized/node_modules/yallist
  yallist@5.0.0:
    - node_modules/yallist
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: hoisted
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Thu, 26 Jun 2025 17:13:15 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /home/<USER>/setup-pnpm/node_modules/.bin/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
