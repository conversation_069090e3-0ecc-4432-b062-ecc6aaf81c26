
import asyncio
import json
from typing import Awaitable
from unittest import TestCase
from unittest.mock import <PERSON><PERSON>ock, patch

from web3 import Web3

from hummingbot.connector.derivative.derive_perpetual.derive_perpetual_auth import DerivePerpetualAuth
from hummingbot.core.web_assistant.connections.data_types import RES<PERSON>ethod, RESTRequest, WSRequest


class DerivePerpetualAuthTests(TestCase):
    def setUp(self) -> None:
        super().setUp()
        self.api_key = "******************************************"
        self.api_secret = "13e56ca9cceebf1f33065c2c5376ab38570a114bc1b003b60d838f92be9d7930"  # noqa: mock
        self.sub_id = "45686"  # noqa: mock
        self.domain = "derive_perpetual_testnet"  # noqa: mock
        self.auth = DerivePerpetualAuth(api_key=self.api_key,
                                        api_secret=self.api_secret,
                                        sub_id=self.sub_id,
                                        trading_required=True,
                                        domain=self.domain)

    def async_run_with_timeout(self, coroutine: Awaitable, timeout: int = 1):
        ret = asyncio.get_event_loop().run_until_complete(asyncio.wait_for(coroutine, timeout))
        return ret

    def test_initialization(self):
        self.assertEqual(self.auth._api_key, self.api_key)
        self.assertEqual(self.auth._api_secret, self.api_secret)
        self.assertEqual(self.auth._sub_id, self.sub_id)
        self.assertTrue(self.auth._trading_required)
        self.assertIsInstance(self.auth._w3, Web3)

    @patch("hummingbot.connector.derivative.derive_perpetual.derive_perpetual_auth.DerivePerpetualAuth.utc_now_ms")
    def test_header_for_authentication(self, mock_utc_now):
        mock_utc_now.return_value = **********
        mock_signature = "0x123signature"

        mock_account = MagicMock()
        mock_account.sign_message.return_value.signature.to_0x_hex.return_value = mock_signature
        self.auth._w3.eth.account = mock_account

        headers = self.auth.header_for_authentication()

        self.assertEqual(headers["accept"], "application/json")
        self.assertEqual(headers["X-LyraWallet"], self.api_key)
        self.assertEqual(headers["X-LyraTimestamp"], "**********")
        self.assertEqual(headers["X-LyraSignature"], mock_signature)

    @patch("hummingbot.core.web_assistant.connections.data_types.WSRequest.send_with_connection")
    def test_ws_authenticate(self, mock_send):
        mock_send.return_value = None
        request = MagicMock(spec=WSRequest)
        request.endpoint = None
        request.payload = {}

        authenticated_request = self.async_run_with_timeout(self.auth.ws_authenticate(request))

        self.assertEqual(authenticated_request.endpoint, request.endpoint)
        self.assertEqual(authenticated_request.payload, request.payload)

    @patch("hummingbot.connector.derivative.derive_perpetual.derive_perpetual_auth.DerivePerpetualAuth.header_for_authentication")
    def test_rest_authenticate(self, mock_header_for_auth):
        mock_header_for_auth.return_value = {"header": "value"}

        request = RESTRequest(
            method=RESTMethod.POST, url="/test", data=json.dumps({"key": "value"}), headers={}
        )

        authenticated_request = self.async_run_with_timeout(self.auth.rest_authenticate(request))

        self.assertIn("header", authenticated_request.headers)
        self.assertEqual(authenticated_request.headers["header"], "value")
        self.assertEqual(authenticated_request.data, json.dumps({"key": "value"}))

    def test_add_auth_to_params_post(self):
        import eth_utils
        address = "******************************************"
        self.assertTrue(eth_utils.is_hex_address(address))
        params = {
            "type": "order",
            # This needs to be 0x40-long
            "asset_address": address,
            "sub_id": 1,
            "limit_price": "100",
            "amount": "10",
            "max_fee": "1",
            "recipient_id": 2,
            "is_bid": True
        }
        request = MagicMock(method=RESTMethod.POST)

        with patch("hummingbot.connector.derivative.derive_perpetual.derive_perpetual_auth.SignedAction.sign") as mock_sign, \
                patch("hummingbot.connector.derivative.derive_perpetual.derive_perpetual_web_utils.order_to_call") as mock_order_to_call:
            mock_order_to_call.return_value = params
            mock_sign.return_value = None

            updated_params = self.auth.add_auth_to_params_post(params, request)
            self.assertIsInstance(updated_params, str)

    @patch("hummingbot.connector.derivative.derive_perpetual.derive_perpetual_auth.DerivePerpetualAuth.utc_now_ms")
    def test_get_ws_auth_payload(self, mock_utc_now):
        mock_utc_now.return_value = **********
        mock_signature = "0x123signature"

        mock_account = MagicMock()
        mock_account.sign_message.return_value.signature.to_0x_hex.return_value = mock_signature
        self.auth._w3.eth.account = mock_account

        payload = self.auth.get_ws_auth_payload()

        self.assertEqual(payload["accept"], "application/json")
        self.assertEqual(payload["wallet"], self.api_key)
        self.assertEqual(payload["timestamp"], "**********")
        self.assertEqual(payload["signature"], mock_signature)

    @patch("hummingbot.connector.derivative.derive_perpetual.derive_perpetual_auth.DerivePerpetualAuth.utc_now_ms")
    def test_utc_now_ms(self, mock_utc_now):
        mock_utc_now.return_value = **********
        timestamp = self.auth.utc_now_ms()
        self.assertEqual(timestamp, **********)
