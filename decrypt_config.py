#!/usr/bin/env python3
"""
Utility script to decrypt Hummingbot configuration values
"""
import binascii
import json
from hummingbot.client.config.config_crypt import ETHKeyFileSecretManger

def decrypt_config_value(encrypted_hex_value: str, password: str) -> str:
    """
    Decrypt a Hummingbot configuration value
    
    Args:
        encrypted_hex_value: The hex-encoded encrypted value from the config file
        password: Your Hummingbot password
        
    Returns:
        The decrypted plaintext value
    """
    try:
        # Convert hex to bytes
        encrypted_bytes = binascii.unhexlify(encrypted_hex_value)
        
        # Decode to string (should be JSON)
        encrypted_json_str = encrypted_bytes.decode()
        
        # Parse JSON
        encrypted_data = json.loads(encrypted_json_str)
        
        # Create secrets manager and decrypt
        secrets_manager = ETHKeyFileSecretManger(password)
        decrypted_value = secrets_manager.decrypt_secret_value("test", encrypted_hex_value)
        
        return decrypted_value
        
    except binascii.Error as e:
        return f"Error: Non-hexadecimal digit found - {e}"
    except json.JSONDecodeError as e:
        return f"Error: Invalid JSON format - {e}"
    except Exception as e:
        return f"Error: {e}"

if __name__ == "__main__":
    # Your encrypted values from the config file
    api_secret_encrypted = "7b2263727970746f223a207b22636970686572223a20226165732d3132382d637472222c2022636970686572706172616d73223a207b226976223a20226330653962333430306530643533633564666539656231623063353633376236227d2c202263697068657274657874223a202235336231656630366261333033393137353431663837306332333262623338373966336134633334643163643865333133303531623363656637386336663133663736346261303037306363326639346437333833303037393531333065396266663031663962663361393231383032333034346534353939366236222c20226b6466223a202270626b646632222c20226b6466706172616d73223a207b2263223a20313030303030302c2022646b6c656e223a2033322c2022707266223a2022686d61632d736861323536222c202273616c74223a20223936396138383439313763653763393131356330656434663233346561623639227d2c20226d6163223a202265363530663364363733663931356163633234353937303465343833636631346261346134313966643232636639353865613737323937326636393037626133227d2c202276657273696f6e223a20332c2022616c696173223a2022227d"
    
    api_key_encrypted = "7b2263727970746f223a207b22636970686572223a20226165732d3132382d637472222c2022636970686572706172616d73223a207b226976223a20226532633831393832356235633933613662666334303030653733633338313465227d2c202263697068657274657874223a2022636132653964303664303263643637393531393734323330333132656534343633656538393439343332663531666434363236313030383065383262616636313963396331646231343834303863623231303339222c20226b6466223a202270626b646632222c20226b6466706172616d73223a207b2263223a20313030303030302c2022646b6c656e223a2033322c2022707266223a2022686d61632d736861323536222c202273616c74223a20223638646664376565366630356636646263666163653439313864396330376665227d2c20226d6163223a202235383762653864653439626532393139313532343931343633656662383264323636366666313662386461383432613736376435616264653265653635653362227d2c202276657273696f6e223a20332c2022616c696173223a2022227d"
    
    # Prompt for password
    password = input("Enter your Hummingbot password: ")
    
    print("\nDecrypting API Secret...")
    api_secret_decrypted = decrypt_config_value(api_secret_encrypted, password)
    print(f"API Secret: {api_secret_decrypted}")
    
    print("\nDecrypting API Key...")
    api_key_decrypted = decrypt_config_value(api_key_encrypted, password)
    print(f"API Key: {api_key_decrypted}")
    
    # Validate the API secret format
    if api_secret_decrypted.startswith("Error:"):
        print(f"\n❌ Decryption failed: {api_secret_decrypted}")
    else:
        # Check if it's a valid hex private key
        clean_key = api_secret_decrypted.replace("0x", "")
        if len(clean_key) == 64 and all(c in '0123456789abcdefABCDEF' for c in clean_key):
            print("✅ API Secret appears to be a valid hex private key")
        else:
            print("❌ API Secret is not a valid hex private key format")
            print(f"   Length: {len(clean_key)} (should be 64)")
            print(f"   Contains non-hex chars: {not all(c in '0123456789abcdefABCDEF' for c in clean_key)}")