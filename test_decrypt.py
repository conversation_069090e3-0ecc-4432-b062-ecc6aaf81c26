#!/usr/bin/env python3
"""
Test actual decryption process
"""
import os
import sys
import binascii
from eth_account import Account

def test_decryption():
    # Your encrypted values from the config file
    api_secret_encrypted = "7b2263727970746f223a207b22636970686572223a20226165732d3132382d637472222c2022636970686572706172616d73223a207b226976223a20226330653962333430306530643533633564666539656231623063353633376236227d2c202263697068657274657874223a202235336231656630366261333033393137353431663837306332333262623338373966336134633334643163643865333133303531623363656637386336663133663736346261303037306363326639346437333833303037393531333065396266663031663962663361393231383032333034346534353939366236222c20226b6466223a202270626b646632222c20226b6466706172616d73223a207b2263223a20313030303030302c2022646b6c656e223a2033322c2022707266223a2022686d61632d736861323536222c202273616c74223a20223936396138383439313763653763393131356330656434663233346561623639227d2c20226d6163223a202265363530663364363733663931356163633234353937303465343833636631346261346134313966643232636639353865613737323937326636393037626133227d2c202276657273696f6e223a20332c2022616c696173223a2022227d"
    
    password = input("Enter your Hummingbot password: ")
    
    try:
        print("Step 1: Converting hex to bytes...")
        encrypted_bytes = binascii.unhexlify(api_secret_encrypted)
        print("✅ Success")
        
        print("Step 2: Decoding bytes to string...")
        encrypted_json_str = encrypted_bytes.decode()
        print("✅ Success")
        
        print("Step 3: Attempting eth_account decryption...")
        decrypted_value = Account.decrypt(encrypted_json_str, password)
        print("✅ Success")
        
        print("Step 4: Decoding final result...")
        final_result = decrypted_value.decode()
        print("✅ Success")
        
        print(f"\n🎉 Decrypted API Secret: {final_result}")
        
        # Validate format
        clean_key = final_result.replace("0x", "")
        if len(clean_key) == 64 and all(c in '0123456789abcdefABCDEF' for c in clean_key):
            print("✅ Valid hex private key format")
        else:
            print("❌ Invalid hex private key format")
            print(f"   Length: {len(clean_key)} (should be 64)")
            print(f"   Contains non-hex chars: {not all(c in '0123456789abcdefABCDEF' for c in clean_key)}")
            
    except binascii.Error as e:
        print(f"❌ Step 1 failed - binascii.Error: {e}")
    except UnicodeDecodeError as e:
        print(f"❌ Step 2 failed - UnicodeDecodeError: {e}")
    except Exception as e:
        print(f"❌ Step 3 failed - {type(e).__name__}: {e}")

if __name__ == "__main__":
    test_decryption()