#!/usr/bin/env python3
"""
Utility script to help diagnose and fix private key format issues for Hyperliquid.
This script will analyze your private key and suggest corrections.
"""

def analyze_private_key(private_key: str):
    """Analyze a private key and provide detailed feedback"""
    print("Private Key Analysis")
    print("=" * 50)
    
    print(f"Original input: '{private_key}'")
    print(f"Length: {len(private_key)} characters")
    
    # Show character-by-character breakdown for debugging
    if len(private_key) <= 70:  # Only show for reasonable lengths
        print("Character breakdown:")
        for i, char in enumerate(private_key):
            if char == ' ':
                print(f"  Position {i:2d}: [SPACE]")
            elif char == '\n':
                print(f"  Position {i:2d}: [NEWLINE]")
            elif char == '\t':
                print(f"  Position {i:2d}: [TAB]")
            elif not char.isprintable():
                print(f"  Position {i:2d}: [NON-PRINTABLE: {ord(char)}]")
            else:
                print(f"  Position {i:2d}: '{char}'")
    
    # Try to clean and analyze
    cleaned = private_key.strip()
    print(f"\nAfter stripping whitespace: '{cleaned}'")
    print(f"Length after stripping: {len(cleaned)} characters")
    
    # Check for 0x prefix
    if cleaned.startswith('0x'):
        without_prefix = cleaned[2:]
        print(f"Has '0x' prefix - without prefix: '{without_prefix}'")
        print(f"Length without '0x': {len(without_prefix)} characters")
        cleaned = without_prefix
    
    # Check if it's valid hex
    try:
        int(cleaned, 16)
        is_valid_hex = True
        print("✓ Contains only valid hexadecimal characters")
    except ValueError:
        is_valid_hex = False
        print("✗ Contains non-hexadecimal characters")
        
        # Find invalid characters
        invalid_chars = []
        for i, char in enumerate(cleaned):
            if char.lower() not in '0123456789abcdef':
                invalid_chars.append((i, char))
        
        if invalid_chars:
            print("Invalid characters found:")
            for pos, char in invalid_chars:
                print(f"  Position {pos}: '{char}' (ASCII: {ord(char)})")
    
    # Length analysis
    if len(cleaned) == 64:
        print("✓ Correct length (64 characters)")
    elif len(cleaned) == 65:
        print("✗ Too long by 1 character (65 instead of 64)")
        print("  This often happens with an extra character at the beginning or end")
    elif len(cleaned) == 63:
        print("✗ Too short by 1 character (63 instead of 64)")
        print("  This might be a truncated key")
    else:
        print(f"✗ Wrong length ({len(cleaned)} instead of 64)")
    
    # Suggestions
    print("\nSuggestions:")
    if len(cleaned) == 65 and is_valid_hex:
        print("1. Check if there's an extra character at the beginning or end")
        print("2. Try removing the first character:")
        print(f"   '{cleaned[1:]}'")
        print("3. Try removing the last character:")
        print(f"   '{cleaned[:-1]}'")
    elif len(cleaned) == 64 and is_valid_hex:
        print("✓ This private key appears to be in the correct format!")
        print(f"Use this: {cleaned}")
    else:
        print("1. Double-check that you copied the complete private key")
        print("2. Make sure you're copying the private key, not the seed phrase or address")
        print("3. Try exporting the private key again from your wallet")

def main():
    print("Hyperliquid Private Key Format Checker")
    print("=" * 50)
    print("This tool will help you diagnose private key format issues.")
    print("WARNING: Never share your actual private key with anyone!")
    print("=" * 50)
    
    # For security, we'll ask the user to paste their key
    print("\nPlease paste your private key below and press Enter:")
    print("(The key will be analyzed locally and not stored or transmitted)")
    
    try:
        private_key = input("Private key: ").strip()
        
        if not private_key:
            print("No private key provided.")
            return
        
        print("\n")
        analyze_private_key(private_key)
        
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
