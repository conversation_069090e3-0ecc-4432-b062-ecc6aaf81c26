repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v2.3.0
    hooks:
    - id: trailing-whitespace
    - id: end-of-file-fixer
    - id: check-yaml
    - id: check-added-large-files
    - id: flake8
      types: ['file']
      files: \.(py|pyx|pxd)$
    - id: detect-private-key
-   repo: https://github.com/hhatto/autopep8
    rev: v2.0.4
    hooks:
    -   id: autopep8
        args: ["--in-place", "--max-line-length=120", "--select=E26,E114,E117,E128,E129,E201,E202,E225,E226,E231,E261,E301,E302,E303,E304,E305,E306,E401,W291,W292,W293,W391"]

-   repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.10.0
    hooks:
    -   id: eslint
        files: \.[jt]sx?$  # *.js, *.jsx, *.ts and *.tsx
        types: [file]
-   repo: https://github.com/CoinAlpha/git-hooks
    rev: 78f0683233a09c68a072fd52740d32c0376d4f0f
    hooks:
    -   id: detect-wallet-private-key
        types: [file]
        exclude: .json
-   repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        files: "\\.(py)$"
        args: [--settings-path=pyproject.toml]
