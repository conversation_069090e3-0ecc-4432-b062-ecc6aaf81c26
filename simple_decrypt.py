#!/usr/bin/env python3
"""
Simple utility to inspect and decrypt Hummingbot configuration values
"""
import binascii
import json

def inspect_encrypted_value(encrypted_hex_value: str):
    """
    Inspect an encrypted value to understand its format
    """
    print(f"Encrypted value length: {len(encrypted_hex_value)}")
    print(f"First 50 chars: {encrypted_hex_value[:50]}...")
    print(f"Last 50 chars: ...{encrypted_hex_value[-50:]}")
    
    # Check if it's valid hex
    try:
        encrypted_bytes = binascii.unhexlify(encrypted_hex_value)
        print("✅ Valid hexadecimal format")
        
        # Try to decode as string
        try:
            encrypted_json_str = encrypted_bytes.decode('utf-8')
            print("✅ Successfully decoded to string")
            
            # Try to parse as JSON
            try:
                encrypted_data = json.loads(encrypted_json_str)
                print("✅ Valid JSON format")
                print("JSON structure:")
                for key in encrypted_data.keys():
                    print(f"  - {key}")
                    
                # Check if it's an eth-account keyfile format
                if 'crypto' in encrypted_data:
                    print("✅ Appears to be eth-account keyfile format")
                    crypto = encrypted_data['crypto']
                    print(f"  Cipher: {crypto.get('cipher', 'unknown')}")
                    print(f"  KDF: {crypto.get('kdf', 'unknown')}")
                else:
                    print("❌ Not eth-account keyfile format")
                    
            except json.JSONDecodeError as e:
                print(f"❌ Invalid JSON: {e}")
                print(f"String content: {encrypted_json_str[:100]}...")
                
        except UnicodeDecodeError as e:
            print(f"❌ Cannot decode as UTF-8 string: {e}")
            
    except binascii.Error as e:
        print(f"❌ Invalid hexadecimal format: {e}")
        
        # Check for common issues
        non_hex_chars = [c for c in encrypted_hex_value if c not in '0123456789abcdefABCDEF']
        if non_hex_chars:
            print(f"Non-hex characters found: {set(non_hex_chars)}")
            
        # Check for odd length
        if len(encrypted_hex_value) % 2 != 0:
            print("❌ Odd length (hex strings must have even length)")

if __name__ == "__main__":
    # Your encrypted values from the config file
    api_secret_encrypted = "7b2263727970746f223a207b22636970686572223a20226165732d3132382d637472222c2022636970686572706172616d73223a207b226976223a20226330653962333430306530643533633564666539656231623063353633376236227d2c202263697068657274657874223a202235336231656630366261333033393137353431663837306332333262623338373966336134633334643163643865333133303531623363656637386336663133663736346261303037306363326639346437333833303037393531333065396266663031663962663361393231383032333034346534353939366236222c20226b6466223a202270626b646632222c20226b6466706172616d73223a207b2263223a20313030303030302c2022646b6c656e223a2033322c2022707266223a2022686d61632d736861323536222c202273616c74223a20223936396138383439313763653763393131356330656434663233346561623639227d2c20226d6163223a202265363530663364363733663931356163633234353937303465343833636631346261346134313966643232636639353865613737323937326636393037626133227d2c202276657273696f6e223a20332c2022616c696173223a2022227d"
    
    api_key_encrypted = "7b2263727970746f223a207b22636970686572223a20226165732d3132382d637472222c2022636970686572706172616d73223a207b226976223a20226532633831393832356235633933613662666334303030653733633338313465227d2c202263697068657274657874223a2022636132653964303664303263643637393531393734323330333132656534343633656538393439343332663531666434363236313030383065383262616636313963396331646231343834303863623231303339222c20226b6466223a202270626b646632222c20226b6466706172616d73223a207b2263223a20313030303030302c2022646b6c656e223a2033322c2022707266223a2022686d61632d736861323536222c202273616c74223a20223638646664376565366630356636646263666163653439313864396330376665227d2c20226d6163223a202235383762653864653439626532393139313532343931343633656662383264323636366666313662386461383432613736376435616264653265653635653362227d2c202276657273696f6e223a20332c2022616c696173223a2022227d"
    
    print("=" * 60)
    print("ANALYZING API SECRET")
    print("=" * 60)
    inspect_encrypted_value(api_secret_encrypted)
    
    print("\n" + "=" * 60)
    print("ANALYZING API KEY")
    print("=" * 60)
    inspect_encrypted_value(api_key_encrypted)